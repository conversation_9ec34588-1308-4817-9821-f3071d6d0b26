# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

PyRT-DICOM is a Python library for creating radiotherapy DICOM files with strongly-typed, IntelliSense-friendly interfaces. The project has evolved from POC to **early implementation phase** with a composition-based architecture using explicit module constructors and ValidationResult objects.

## Core Architecture

### Design Philosophy
- **Composition over inheritance**: IODs use module composition instead of complex multiple inheritance
- **Explicit constructors**: Each module has type-safe `from_required_elements()` and `with_optional_elements()` builders  
- **On-demand dataset generation**: IODs generate fresh pydicom datasets from modules when needed
- **Structured validation**: ValidationResult objects provide consistent error/warning handling

### Key Architectural Decisions
- **BaseIOD with module storage**: IODs store live module references and generate datasets dynamically
- **ValidationResult migration**: Replaced dict-based validation with structured ValidationResult class
- **Module organization**: Modules grouped under `modules/` with corresponding validators under `validators/modules/`
- **Type-safe builders**: All modules use fluent API pattern for construction with type hints

### Project Structure
```
src/pyrt_dicom/
├── __init__.py                    # Package initialization
├── enums/                         # DICOM enumerated values
│   ├── dose_enums.py
│   └── clinical_trial_enums.py
├── iods/                          # Information Object Definitions
│   ├── base_iod.py               # Base IOD with composition pattern
│   └── rt_dose_iod.py            # RT Dose IOD implementation
├── modules/modules/               # DICOM modules (50+ modules)
│   ├── base_module.py            # Base module pattern
│   ├── patient_module.py
│   ├── rt_dose_module.py
│   └── ...
└── validators/                    # Validation system
    ├── validation_result.py       # Structured validation results
    └── modules/                   # Module-specific validators
        ├── base_validator.py
        ├── patient_validator.py
        └── ...
```

## Development Commands

### Environment Setup
```bash
# Install in development mode with test dependencies
pip install -e .[test]

# Alternative: Install base package only
pip install -e .
```

### Testing
```bash
# Run all tests (48+ test files)
pytest

# Run tests with coverage
pytest --cov=src/pyrt_dicom --cov-report=html

# Run specific test categories
pytest tests/unit/modules/ -v                    # Module tests
pytest tests/unit/validators/ -v                 # Validator tests  
pytest tests/unit/iods/ -v                      # IOD tests

# Run specific test file
pytest tests/unit/modules/test_patient_module.py -v
```

### Type Checking
```bash
# Type check with Hatch environment (recommended)
hatch run types:check

# Direct mypy (if installed separately)
mypy src/pyrt_dicom tests
```

### Code Quality
```bash
# Lint with Ruff (included in test dependencies)
ruff check src/ tests/

# Format with Ruff
ruff format src/ tests/
```

## Current Implementation Status

### Completed (Core Architecture)
- ✅ BaseIOD composition pattern with module storage and dataset generation  
- ✅ ValidationResult class with structured error/warning handling
- ✅ RT Dose IOD with comprehensive module support (50+ modules implemented)
- ✅ Module builders with `from_required_elements()` and `with_optional_elements()` patterns
- ✅ All DICOM module validators migrated to ValidationResult objects
- ✅ Enums for dose and clinical trial coded values
- ✅ Build system with hatch and comprehensive dependencies

### In Progress (Validation & Testing)
- 🔄 Pytest test suite updates for ValidationResult migration
- 🔄 Integration testing with real DICOM file generation
- 🔄 Module validation rule completeness

### Future Phases (Expansion)
- ⏳ Additional IODs (RT Plan, RT Structure Set, RT Image)
- ⏳ Advanced dose calculation utilities
- ⏳ DICOM file validation tools
- ⏳ Performance optimization and memory efficiency

## Implementation Patterns

### Creating New Modules
```python
from pydicom import Dataset
from ..validators import ValidationResult

class NewModule(Dataset):
    """Module description - DICOM PS3.3 reference.
    
    Usage:
        module = NewModule.from_required_elements(
            required_param="value"
        ).with_optional_elements(
            optional_param="value"
        )
    """
    
    @classmethod
    def from_required_elements(cls, *args, **kwargs) -> 'NewModule':
        """Create module with all required (Type 1 and Type 2) elements."""
        instance = cls()
        # Set required elements with proper DICOM tags
        return instance
    
    def with_optional_elements(self, **kwargs) -> 'NewModule':
        """Add optional (Type 3) elements."""
        # Set optional elements
        return self
    
    def validate(self, config=None) -> ValidationResult:
        """Validate module data using ValidationResult."""
        from ..validators.modules.new_validator import NewValidator
        return NewValidator.validate(self, config)
```

### Creating New IODs
```python
from .base_iod import BaseIOD
from ..modules import RequiredModule1, RequiredModule2

class NewIOD(BaseIOD):
    """IOD description - DICOM PS3.3 reference."""
    
    SOP_CLASS_UID = "1.2.840.10008.5.1.4.x.x.x"
    
    def __init__(self, 
                 required_module1: RequiredModule1,
                 required_module2: RequiredModule2,
                 optional_module: Optional[OptionalModule] = None):
        """Create IOD from constituent modules."""
        super().__init__()
        
        self._modules = {
            'required1': required_module1,
            'required2': required_module2
        }
        
        if optional_module:
            self._modules['optional'] = optional_module
    
    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific requirements."""
        # IOD-specific validation logic
        pass
```

## Important Development Guidelines

### Architecture Constraints  
- **Composition-first**: IODs must use module composition, not inheritance
- **ValidationResult consistency**: All validation methods must return ValidationResult objects
- **Type safety**: Maintain complete type hints and IntelliSense support
- **Module independence**: Modules should be testable and usable independently

### Testing Requirements
- All modules must have comprehensive pytest tests covering ValidationResult objects
- Test both successful operations and validation failures with proper ValidationResult assertions
- Include DICOM compliance validation tests 
- Module tests should verify builder patterns (`from_required_elements`, `with_optional_elements`)
- IOD tests should verify composition and dataset generation

### Code Quality Standards  
- Google-style docstrings for all classes and methods with Sphinx compatibility
- Complete type hints using modern syntax (`str | None` instead of `Optional[str]`)
- ValidationResult objects instead of dict-based validation results
- Enum usage for all DICOM coded values (no free text strings)
- Module validators must be in `validators/modules/` with corresponding names

### DICOM Compliance
- Follow DICOM PS3.3 specifications exactly
- Implement Type 1, 1C, 2, 2C, and 3 requirements correctly
- Use proper DICOM data element tags and VRs
- Generate valid DICOM files that pass external validation tools

## Dependencies

### Core Dependencies
- `pydicom>=2.4.0`: Core DICOM functionality
- `python>=3.11`: Modern Python features and type hints
- `numpy>=1.24.0`: Array operations for dose data

### Development Dependencies  
- `pytest>=7.0.0`: Testing framework
- `pytest-cov>=4.0.0`: Coverage reporting
- `ruff>=0.12.0`: Linting and formatting

## Build System

The project uses **hatch** as the build system with the following key configurations:
- Package metadata and dependencies in `pyproject.toml`
- Version management from `src/pyrt_dicom/__about__.py`
- Test environment with pytest and coverage reporting
- Type checking environment with mypy
- Ruff integration for linting and formatting

## Current Development Goals

### Phase 1 - Core Stability ✅
1. ✅ Complete BaseIOD composition architecture  
2. ✅ Migrate all validators to ValidationResult objects
3. ✅ RT Dose IOD with full module support

### Phase 2 - Testing & Validation 🔄
1. 🔄 Update pytest suite for ValidationResult migration
2. 🔄 Achieve >90% test coverage with comprehensive validation tests  
3. 🔄 Generate and validate real DICOM files with external tools
4. ⏳ Performance benchmarking and optimization

## Architecture Benefits

The composition-based architecture provides significant advantages:

**✅ Clean Architecture**:
- IODs inherit from BaseIOD with simple module storage
- No complex multiple inheritance chains
- Clear separation between data (modules) and orchestration (IODs)

**✅ Type-Safe Composition**:
- Modules have explicit constructor parameters with full type hints
- IODs generate datasets on-demand from live module references
- IntelliSense works perfectly through `get_module()` access patterns

**✅ Structured Validation**:
- ValidationResult objects provide consistent error/warning handling
- Module validators are independently testable and maintainable
- Clear validation flows from modules → IODs → complete datasets

**✅ DICOM File Generation**:
- `generate_dataset()` creates pydicom Dataset objects for manipulation
- `generate_file_dataset()` creates FileDataset objects for direct file I/O
- Proper DICOM file meta information and transfer syntax handling

**✅ Developer Experience**:
- Builder patterns make module creation intuitive and safe
- Live module references allow post-construction modifications
- Comprehensive property access for dose statistics and spatial information

**⚠️ DEVELOPMENT GUIDELINES ⚠️**
- Follow composition patterns - no complex inheritance hierarchies
- Use ValidationResult objects consistently - never return dict-based validation
- Always add Google-style docstrings with Sphinx compatibility
- Use modern type hints: `str | None` instead of `Optional[str]`, `int | float` instead of `Union[int, float]`, `list` instead of `List`, `dict` instead of `Dict`
- Only use `Any` (`from typing import Any`) when absolutely necessary

