"""Base IOD for DICOM Information Object Definitions.

Provides foundation for all DICOM IOD implementations using explicit module
constructors and on-demand dataset generation.
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
import pydicom
from pydicom import config
from pydicom.uid import UID, ExplicitVRLittleEndian
from ..validators import ValidationResult
from ..validators.modules.base_validator import ValidationConfig


class IODValidationError(Exception):
    """Raised when IOD module requirements are not satisfied."""
    pass


class BaseIOD(ABC):
    """Base class for all DICOM Information Object Definitions.
    
    Provides module storage, dataset generation, and validation framework
    for DICOM IOD implementations. Uses composition over inheritance with
    explicit module constructors.
    """
    
    def __init__(self):
        self._modules: Dict[str, Any] = {}
    
    def generate_dataset(self) -> pydicom.Dataset:
        """Generate a fresh DICOM dataset from current module state.
        
        This method explicitly converts the collection of modules into a pydicom
        Dataset by merging all module data. Each call generates a new dataset
        reflecting the current state of all modules.
        
        Returns:
            Fresh DICOM dataset containing all module data
            
        Example:
            # Create IOD with modules
            rt_dose = RTDoseIOD(patient_module, study_module, ...)
            
            # Generate dataset for export
            dataset = rt_dose.generate_dataset()
            dataset.save_as("dose.dcm")
            
            # Modify modules
            rt_dose.get_module('rt_dose').dose_grid_scaling = 0.002
            
            # Generate fresh dataset with updated data
            updated_dataset = rt_dose.generate_dataset()
            updated_dataset.save_as("dose_updated.dcm")
        """
        ds = pydicom.Dataset()
        for module in self._modules.values():
            ds.update(module)
        return ds
    
    def generate_file_dataset(self, filename: str = "") -> pydicom.FileDataset:
        """Generate a DICOM FileDataset ready for file I/O operations.
        
        This method creates a FileDataset with proper file meta information
        for direct saving to DICOM files. Use this when you need the full
        file dataset structure with DICOM file headers.
        
        Args:
            filename: The filename for the DICOM file (optional)
        
        Returns:
            FileDataset with complete DICOM file structure
            
        Example:
            # Generate file dataset for saving
            file_dataset = rt_dose.generate_file_dataset("dose_with_meta.dcm")
            file_dataset.save_as("dose_with_meta.dcm")
        """
        ds = self.generate_dataset()
        
        # Create file meta information
        file_meta = pydicom.FileMetaDataset()
        file_meta.MediaStorageSOPClassUID = UID(getattr(ds, 'SOPClassUID', ''), config.IGNORE)
        file_meta.MediaStorageSOPInstanceUID = UID(getattr(ds, 'SOPInstanceUID', ''), config.IGNORE)
        file_meta.ImplementationClassUID = UID("1.2.826.0.1.3680043.8.498.1")  # PyRT-DICOM
        file_meta.ImplementationVersionName = "PyRT-DICOM 0.1.0"
        file_meta.TransferSyntaxUID = ExplicitVRLittleEndian
        
        # Create FileDataset
        file_ds = pydicom.FileDataset(
            filename,
            dataset=ds,
            file_meta=file_meta,
            is_implicit_VR=False,
            is_little_endian=True
        )
        
        return file_ds
    
    def get_module(self, module_name: str) -> Optional[Any]:
        """Get reference to a specific module for direct modification.
        
        Args:
            module_name: Name of module (e.g., 'patient', 'study', 'series')
            
        Returns:
            Live reference to module, or None if not present
            
        Note:
            Modifying returned module will affect subsequent calls to
            generate_dataset() and generate_file_dataset().
        """
        return self._modules.get(module_name)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate entire IOD including all modules.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
        
        Returns:
            ValidationResult with errors and warnings
        """
        result = ValidationResult()
        
        # Validate each module
        for name, module in self._modules.items():
            if hasattr(module, 'validate'):
                try:
                    module_result = module.validate(config)
                    # Handle both ValidationResult and dict-based results
                    if hasattr(module_result, 'errors') and hasattr(module_result, 'warnings'):
                        # ValidationResult object
                        for error in module_result.errors:
                            result.add_error(f"{name}: {error}")
                        for warning in module_result.warnings:
                            result.add_warning(f"{name}: {warning}")
                    elif isinstance(module_result, dict):
                        # Legacy dict-based result
                        for error in module_result.get('errors', []):
                            result.add_error(f"{name}: {error}")
                        for warning in module_result.get('warnings', []):
                            result.add_warning(f"{name}: {warning}")
                except Exception as e:
                    result.add_error(f"Validation error in {name}: {e}")
        
        # IOD-specific validation - delegate to subclass
        # Subclasses should override this method to provide specific validation
        
        return result
    
    def _validate_base_iod_requirements(self, result: ValidationResult) -> None:
        """Validate base IOD requirements.
        
        Args:
            result: ValidationResult to update
        """
        # Basic IOD validation - subclasses can call this if needed
        dataset = self.generate_dataset()
        
        if not hasattr(dataset, 'SOPClassUID') or not dataset.SOPClassUID:
            result.add_error("SOPClassUID is required")
        
        if not hasattr(dataset, 'SOPInstanceUID') or not dataset.SOPInstanceUID:
            result.add_error("SOPInstanceUID is required")
    
    @abstractmethod
    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.
        
        This method must be implemented by each IOD subclass to validate
        their specific module requirements and dependencies.
        """
        pass
    
    def __repr__(self) -> str:
        """String representation of the IOD."""
        class_name = self.__class__.__name__
        modules = list(self._modules.keys())
        dataset = self.generate_dataset()
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', 'Unknown')
        return f"{class_name}(modules={modules}, sop_instance_uid='{sop_instance_uid}')"