"""
Cine Module - DICOM PS3.3 C.7.6.5

The Cine Module describes a Multi-frame Cine Image with timing, playback, and audio channel support.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.image_enums import PreferredPlaybackSequencing, ChannelMode
from ...validators.modules.cine_validator import CineValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult


class CineModule(BaseModule):
    """Cine Module implementation for DICOM PS3.3 C.7.6.5.
    
    Inherits from BaseModule to provide native DICOM data handling with user-friendly API.
    Describes a Multi-frame Cine Image with timing information and optional audio channels.
    
    The Cine Module contains only Type 1C (conditional) and Type 3 (optional) elements.
    Type 1C elements depend on the Frame Increment Pointer (0028,0009) from the Multi-frame Module.
    
    Usage:
        # Create empty module (no Type 1 or Type 2 elements)
        cine = CineModule.from_required_elements() # or CineModule() for short
        
        # Add conditional elements based on frame increment pointer
        cine.with_frame_time(frame_time=33.33)  # 30 fps
        
        # Or use frame time vector instead
        cine.with_frame_time_vector(
            frame_time_vector=[0, 33.33, 33.33, 33.33, 33.33]
        )
        
        # Add optional elements
        cine.with_optional_elements(
            preferred_playback_sequencing=PreferredPlaybackSequencing.LOOPING,
            recommended_display_frame_rate=30.0,
            cine_rate=30.0,
            start_trim=1,
            stop_trim=100
        )
        
        # Add audio channels if transfer syntax contains multiplexed audio
        audio_channel = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode=ChannelMode.MONO,
            source_code_value="MAIN",
            source_coding_scheme="DCM"
        )
        cine.with_audio_channels([audio_channel])
        
        # Validate
        result = cine.validate()
    """
    
    @classmethod
    def from_required_elements(cls) -> 'CineModule':
        """Create CineModule with all required (Type 1 and Type 2) elements.
        
        The Cine Module has no Type 1 or Type 2 elements - all elements are either
        Type 1C (conditional) or Type 3 (optional). This method returns an empty instance.
        
        Use conditional methods like with_frame_time() for Type 1C elements that depend
        on the Frame Increment Pointer (0028,0009) from the Multi-frame Module.
        
        Returns:
            CineModule: New empty module instance
        """
        return cls()
    
    def with_frame_time(
        self,
        frame_time: float,
        frame_increment_pointer_value: str | None = None
    ) -> 'CineModule':
        """Add Frame Time (Type 1C when Frame Increment Pointer points to it).
        
        Frame Time is required if Frame Increment Pointer (0028,0009) points to this attribute.
        This creates a conditional requirement that can only be fully validated with access to
        the Multi-frame Module's Frame Increment Pointer.
        
        Args:
            frame_time (float): Nominal time in msec per individual frame (0018,1063) Type 1C
            frame_increment_pointer_value (str | None): Value of Frame Increment Pointer for validation.
                If None, conditional validation is limited.
            
        Returns:
            CineModule: Self with Frame Time added
        """
        # Validate conditional requirement if Frame Increment Pointer is available
        if frame_increment_pointer_value is not None:
            self._validate_conditional_requirement(
                frame_increment_pointer_value == "(0018,1063)",  # Points to Frame Time
                [frame_time],
                "Frame Time (0018,1063) is required when Frame Increment Pointer points to it"
            )
        
        self.FrameTime = frame_time
        return self
    
    def with_frame_time_vector(
        self,
        frame_time_vector: list[float],
        frame_increment_pointer_value: str | None = None
    ) -> 'CineModule':
        """Add Frame Time Vector (Type 1C when Frame Increment Pointer points to it).
        
        Frame Time Vector is required if Frame Increment Pointer (0028,0009) points to this attribute.
        The first frame always has a time increment of 0.
        
        Args:
            frame_time_vector (list[float]): Time increments in msec between frames (0018,1065) Type 1C.
                First frame always has time increment of 0.
            frame_increment_pointer_value (str | None): Value of Frame Increment Pointer for validation.
                If None, conditional validation is limited.
            
        Returns:
            CineModule: Self with Frame Time Vector added
        """
        # Validate conditional requirement if Frame Increment Pointer is available
        if frame_increment_pointer_value is not None:
            self._validate_conditional_requirement(
                frame_increment_pointer_value == "(0018,1065)",  # Points to Frame Time Vector
                [frame_time_vector],
                "Frame Time Vector (0018,1065) is required when Frame Increment Pointer points to it"
            )
        
        # Validate first frame increment is 0
        if frame_time_vector and frame_time_vector[0] != 0:
            # This is a warning rather than error since it's a standard recommendation
            pass  # Warning will be handled in validator
        
        self.FrameTimeVector = frame_time_vector
        return self

    def with_audio_channels(
        self,
        audio_channels: list[Dataset] | None = None,
        transfer_syntax_has_audio: bool | None = None
    ) -> 'CineModule':
        """Add multiplexed audio channels (Type 2C when transfer syntax contains audio).
        
        Multiplexed Audio Channels Description Code Sequence is required if the Transfer 
        Syntax contains multiplexed (interleaved) audio channels. Can be present but empty
        if no audio was recorded.
        
        Args:
            audio_channels (list[Dataset] | None): Audio channel descriptions (003A,0300) Type 2C.
                Use create_audio_channel_item() to create proper Dataset objects.
            transfer_syntax_has_audio (bool | None): Whether transfer syntax contains audio.
                If None, conditional validation is limited.
            
        Returns:
            CineModule: Self with audio channels added
        """
        # Validate conditional requirement if transfer syntax info is available
        if transfer_syntax_has_audio is not None:
            self._validate_conditional_requirement(
                transfer_syntax_has_audio,
                [audio_channels] if audio_channels is not None else [[]],  # Can be empty list
                "Multiplexed Audio Channels Description Code Sequence (003A,0300) is required when transfer syntax contains audio"
            )
        
        if audio_channels is not None:
            # Ensure all items are Dataset objects (should be from create_audio_channel_item)
            for i, channel in enumerate(audio_channels):
                if not isinstance(channel, Dataset):
                    raise ValueError(f"Audio channel item {i} must be a Dataset object. Use create_audio_channel_item() to create proper items.")
            
            self.MultiplexedAudioChannelsDescriptionCodeSequence = audio_channels
        else:
            # Set empty sequence if explicitly setting to None but audio is required
            if transfer_syntax_has_audio:
                self.MultiplexedAudioChannelsDescriptionCodeSequence = []
        
        return self
    
    def with_optional_elements(
        self,
        preferred_playback_sequencing: int | PreferredPlaybackSequencing | None = None,
        start_trim: int | None = None,
        stop_trim: int | None = None,
        recommended_display_frame_rate: float | None = None,
        cine_rate: float | None = None,
        frame_delay: float | None = None,
        image_trigger_delay: float | None = None,
        effective_duration: float | None = None,
        actual_frame_duration: float | None = None
    ) -> 'CineModule':
        """Add optional (Type 3) data elements.
        
        Args:
            preferred_playback_sequencing (int | PreferredPlaybackSequencing | None): Playback method (0018,1244) Type 3
            start_trim (int | None): First frame to display (0008,2142) Type 3
            stop_trim (int | None): Last frame to display (0008,2143) Type 3
            recommended_display_frame_rate (float | None): Display rate in frames/sec (0008,2144) Type 3
            cine_rate (float | None): Frames per second (0018,0040) Type 3
            frame_delay (float | None): Time from Content Time to first frame in msec (0018,1066) Type 3
            image_trigger_delay (float | None): Delay from trigger to first frame in msec (0018,1067) Type 3
            effective_duration (float | None): Total acquisition time in seconds (0018,0072) Type 3
            actual_frame_duration (float | None): Elapsed acquisition time per frame in msec (0018,1242) Type 3
            
        Returns:
            CineModule: Self with optional elements added
        """
        if preferred_playback_sequencing is not None:
            self.PreferredPlaybackSequencing = self._format_enum_value(preferred_playback_sequencing)
        self._set_attribute_if_not_none('StartTrim', start_trim)
        self._set_attribute_if_not_none('StopTrim', stop_trim)
        self._set_attribute_if_not_none('RecommendedDisplayFrameRate', recommended_display_frame_rate)
        self._set_attribute_if_not_none('CineRate', cine_rate)
        self._set_attribute_if_not_none('FrameDelay', frame_delay)
        self._set_attribute_if_not_none('ImageTriggerDelay', image_trigger_delay)
        self._set_attribute_if_not_none('EffectiveDuration', effective_duration)
        self._set_attribute_if_not_none('ActualFrameDuration', actual_frame_duration)
        return self
    
    @staticmethod
    def create_audio_channel_item(
        channel_id: int,
        channel_mode: str | ChannelMode,
        source_code_value: str,
        source_coding_scheme: str = "DCM",
        source_code_meaning: str | None = None
    ) -> Dataset:
        """Create audio channel sequence item.
        
        Args:
            channel_id (int): Channel identification code (1 for main, 2+ for additional)
            channel_mode (str | ChannelMode): Channel mode (MONO/STEREO)
            source_code_value (str): Audio source code value
            source_coding_scheme (str): Coding scheme designator (default: "DCM")
            source_code_meaning (str | None): Human-readable meaning
            
        Returns:
            Dataset: Audio channel sequence item
        """
        channel_source_item = Dataset()
        channel_source_item.CodeValue = source_code_value
        channel_source_item.CodingSchemeDesignator = source_coding_scheme
        if source_code_meaning:
            channel_source_item.CodeMeaning = source_code_meaning
        
        item = Dataset()
        item.ChannelIdentificationCode = channel_id
        if isinstance(channel_mode, ChannelMode):
            item.ChannelMode = channel_mode.value
        else:
            item.ChannelMode = channel_mode
        item.ChannelSourceSequence = [channel_source_item]
        return item
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is present.
        
        Returns:
            bool: True if Frame Time element is present
        """
        return hasattr(self, 'FrameTime')
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is present.
        
        Returns:
            bool: True if Frame Time Vector element is present
        """
        return hasattr(self, 'FrameTimeVector')
    
    @property
    def has_audio_channels(self) -> bool:
        """Check if audio channels are defined.
        
        Returns:
            bool: True if multiplexed audio channels are configured
        """
        return hasattr(self, 'MultiplexedAudioChannelsDescriptionCodeSequence')
    
    @property
    def audio_channel_count(self) -> int:
        """Get number of audio channels.
        
        Returns:
            int: Number of configured audio channels, 0 if none
        """
        if not self.has_audio_channels:
            return 0
        return len(self.MultiplexedAudioChannelsDescriptionCodeSequence)
    
    @property
    def effective_frame_rate(self) -> float | None:
        """Calculate effective frame rate from available timing information.
        
        Returns:
            float | None: Frame rate in frames per second, or None if cannot determine
        """
        if hasattr(self, 'CineRate'):
            return self.CineRate
        elif hasattr(self, 'RecommendedDisplayFrameRate'):
            return self.RecommendedDisplayFrameRate
        elif hasattr(self, 'FrameTime'):
            # Convert from msec per frame to frames per second
            return 1000.0 / self.FrameTime if self.FrameTime > 0 else None
        return None
    
    def calculate_frame_relative_time(self, frame_number: int) -> float | None:
        """Calculate relative time for a specific frame.
        
        Args:
            frame_number: Frame number (1-based)
            
        Returns:
            Relative time in msec, or None if cannot calculate
        """
        if frame_number < 1:
            return None
        
        frame_delay = getattr(self, 'FrameDelay', 0)
        
        if self.uses_frame_time:
            # Frame 'Relative Time' (n) = Frame Delay + Frame Time * (n-1)
            return frame_delay + self.FrameTime * (frame_number - 1)
        elif self.uses_frame_time_vector:
            # Sum time increments up to frame n
            if frame_number > len(self.FrameTimeVector):
                return None
            return frame_delay + sum(self.FrameTimeVector[:frame_number])
        
        return None
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Cine Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return CineValidator.validate(self, config)
