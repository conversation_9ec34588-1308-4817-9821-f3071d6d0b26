"""General Image Module DICOM validation - PS3.3 C.7.6.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod
)


class GeneralImageValidator:
    """Validator for DICOM General Image Module (PS3.3 C.7.6.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            GeneralImageValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            GeneralImageValidator._validate_sequence_requirements(dataset, result)
        
        # Validate lossy compression consistency
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_lossy_compression_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements."""
        
        # Type 2C: Content Date and Content Time for temporally related series
        # Note: We can't determine if series is temporally related from this module alone,
        # so we'll just warn if one is present without the other
        has_content_date = hasattr(dataset, 'ContentDate')
        has_content_time = hasattr(dataset, 'ContentTime')
        
        if has_content_date and not has_content_time:
            result.add_warning(
                "Content Time (0008,0033) should be present when Content Date (0008,0023) is present "
                "for temporally related series"
            )
        elif has_content_time and not has_content_date:
            result.add_warning(
                "Content Date (0008,0023) should be present when Content Time (0008,0033) is present "
                "for temporally related series"
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Quality Control Image
        if hasattr(dataset, 'QualityControlImage'):
            valid_values = [e.value for e in QualityControlImage]
            if dataset.QualityControlImage not in valid_values:
                result.add_error(
                    f"Quality Control Image (0028,0300) has invalid value '{dataset.QualityControlImage}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Burned In Annotation
        if hasattr(dataset, 'BurnedInAnnotation'):
            valid_values = [e.value for e in BurnedInAnnotation]
            if dataset.BurnedInAnnotation not in valid_values:
                result.add_error(
                    f"Burned In Annotation (0028,0301) has invalid value '{dataset.BurnedInAnnotation}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Recognizable Visual Features
        if hasattr(dataset, 'RecognizableVisualFeatures'):
            valid_values = [e.value for e in RecognizableVisualFeatures]
            if dataset.RecognizableVisualFeatures not in valid_values:
                result.add_error(
                    f"Recognizable Visual Features (0028,0302) has invalid value '{dataset.RecognizableVisualFeatures}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Lossy Image Compression
        if hasattr(dataset, 'LossyImageCompression'):
            valid_values = [e.value for e in LossyImageCompression]
            if dataset.LossyImageCompression not in valid_values:
                result.add_error(
                    f"Lossy Image Compression (0028,2110) has invalid value '{dataset.LossyImageCompression}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Presentation LUT Shape
        if hasattr(dataset, 'PresentationLUTShape'):
            valid_values = [e.value for e in PresentationLUTShape]
            if dataset.PresentationLUTShape not in valid_values:
                result.add_error(
                    f"Presentation LUT Shape (2050,0020) has invalid value '{dataset.PresentationLUTShape}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Image Laterality
        if hasattr(dataset, 'ImageLaterality'):
            valid_values = [e.value for e in ImageLaterality]
            if dataset.ImageLaterality not in valid_values:
                result.add_error(
                    f"Image Laterality (0020,0062) has invalid value '{dataset.ImageLaterality}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Lossy Image Compression Method
        if hasattr(dataset, 'LossyImageCompressionMethod'):
            valid_values = [e.value for e in LossyImageCompressionMethod]
            methods = dataset.LossyImageCompressionMethod
            if isinstance(methods, str):
                methods = [methods]
            for method in methods:
                if method not in valid_values:
                    result.add_error(
                        f"Lossy Image Compression Method (0028,2114) has invalid value '{method}'. "
                        f"Valid values: {valid_values}"
                    )
        
        # Image Type validation
        if hasattr(dataset, 'ImageType'):
            image_type = dataset.ImageType
            if isinstance(image_type, list) and len(image_type) >= 2:
                # Value 1 - Pixel Data Characteristics
                if image_type[0] not in ["ORIGINAL", "DERIVED"]:
                    result.add_error(
                        f"Image Type (0008,0008) Value 1 has invalid value '{image_type[0]}'. "
                        "Valid values: ORIGINAL, DERIVED"
                    )
                # Value 2 - Patient Examination Characteristics
                if image_type[1] not in ["PRIMARY", "SECONDARY"]:
                    result.add_error(
                        f"Image Type (0008,0008) Value 2 has invalid value '{image_type[1]}'. "
                        "Valid values: PRIMARY, SECONDARY"
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Icon Image Sequence validation
        if hasattr(dataset, 'IconImageSequence'):
            if len(dataset.IconImageSequence) > 1:
                result.add_error(
                    "Icon Image Sequence (0088,0200) may contain only a single Item"
                )
            
            if len(dataset.IconImageSequence) == 1:
                icon_item = dataset.IconImageSequence[0]
                
                # Required attributes for icon image
                required_attrs = ['Rows', 'Columns', 'SamplesPerPixel', 'PhotometricInterpretation',
                                'BitsAllocated', 'BitsStored', 'HighBit', 'PixelRepresentation']
                
                for attr in required_attrs:
                    if not hasattr(icon_item, attr):
                        result.add_error(
                            f"Icon Image Sequence item missing required attribute {attr}"
                        )
                
                # Validate icon image constraints
                if hasattr(icon_item, 'SamplesPerPixel') and icon_item.SamplesPerPixel != 1:
                    if hasattr(icon_item, 'PhotometricInterpretation'):
                        if icon_item.PhotometricInterpretation not in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]:
                            result.add_error(
                                "Icon Image must use monochrome or palette color photometric interpretation"
                            )
                
                if hasattr(icon_item, 'BitsAllocated'):
                    if icon_item.BitsAllocated not in [1, 8]:
                        result.add_error(
                            "Icon Image Bits Allocated must be 1 or 8"
                        )
    
    @staticmethod
    def _validate_lossy_compression_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate lossy compression attribute consistency."""
        
        has_lossy_compression = hasattr(dataset, 'LossyImageCompression')
        has_compression_ratio = hasattr(dataset, 'LossyImageCompressionRatio')
        has_compression_method = hasattr(dataset, 'LossyImageCompressionMethod')
        
        # If lossy compression is "01", ratio and method should be present
        if has_lossy_compression and dataset.LossyImageCompression == "01":
            if not has_compression_ratio:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'"
                )
            if not has_compression_method:
                result.add_warning(
                    "Lossy Image Compression Method (0028,2114) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'"
                )
        
        # If ratio and method are both present, they should have corresponding values
        if has_compression_ratio and has_compression_method:
            ratio_count = len(dataset.LossyImageCompressionRatio) if isinstance(dataset.LossyImageCompressionRatio, list) else 1
            method_count = len(dataset.LossyImageCompressionMethod) if isinstance(dataset.LossyImageCompressionMethod, list) else 1
            
            if ratio_count != method_count:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) and Method (0028,2114) "
                    "should have corresponding number of values"
                )
