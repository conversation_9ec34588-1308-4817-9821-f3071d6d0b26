"""
Test RTDoseIOD (RT Dose Information Object Definition) functionality.

RTDoseIOD implements DICOM PS3.3 A.18.3 RT Dose IOD with module composition
and conditional module dependencies.
"""

import pytest
import numpy as np
from pydicom.uid import generate_uid
from unittest.mock import Mock
from pydicom import Dataset

from pyrt_dicom.iods import RTDoseIOD
from pyrt_dicom.iods.base_iod import IODValidationError
from pyrt_dicom.modules import (
    PatientModule, GeneralStudyModule, RTSeriesModule, FrameOfReferenceModule,
    GeneralEquipmentModule, RTDoseModule, SOPCommonModule, GeneralImageModule,
    ImagePlaneModule, ImagePixelModule, MultiFrameModule, RTDVHModule,
    OverlayPlaneModule, ApprovalModule, FrameExtractionModule
)
from pyrt_dicom.enums import PatientSex, DoseUnits, DoseType, DoseSummationType


def create_mock_module(**attributes):
    """Create a mock module with specified attributes."""
    mock = Mock(spec=Dataset)
    for attr, value in attributes.items():
        setattr(mock, attr, value)
    return mock


def create_mock_patient_module():
    """Create a mock patient module."""
    return create_mock_module(
        PatientsName="Doe^John^^",
        PatientID="12345",
        PatientsBirthDate="19900101",
        PatientsSex="M"
    )


def create_mock_general_study_module():
    """Create a mock general study module."""
    return create_mock_module(
        StudyInstanceUID=generate_uid(),
        StudyDate="20240101",
        StudyTime="120000"
    )


def create_mock_rt_series_module():
    """Create a mock RT series module."""
    return create_mock_module(
        Modality="RTDOSE",
        SeriesInstanceUID=generate_uid(),
        SeriesNumber="1"
    )


def create_mock_frame_of_reference_module():
    """Create a mock frame of reference module."""
    return create_mock_module(
        FrameOfReferenceUID=generate_uid()
    )


def create_mock_general_equipment_module():
    """Create a mock general equipment module."""
    return create_mock_module(
        Manufacturer="Test System"
    )


def create_mock_sop_common_module():
    """Create a mock SOP common module."""
    return create_mock_module(
        SOPClassUID="1.2.840.10008.*******.1.481.2",
        SOPInstanceUID=generate_uid()
    )


def create_mock_general_image_module():
    """Create a mock general image module."""
    return create_mock_module(
        InstanceNumber="1",
        PatientOrientation=""
    )


def create_mock_image_plane_module():
    """Create a mock image plane module."""
    return create_mock_module(
        PixelSpacing=[2.5, 2.5],
        ImageOrientationPatient=[1, 0, 0, 0, 1, 0],
        ImagePositionPatient=[0.0, 0.0, 0.0],
        SliceThickness=2.5
    )


def create_mock_image_pixel_module():
    """Create a mock image pixel module."""
    return create_mock_module(
        SamplesPerPixel=1,
        PhotometricInterpretation="MONOCHROME2",
        Rows=64,
        Columns=64,
        BitsAllocated=32,
        BitsStored=32,
        HighBit=31,
        PixelRepresentation=0
    )


def create_mock_multi_frame_module():
    """Create a mock multi-frame module."""
    return create_mock_module(
        NumberOfFrames=10
    )


def create_mock_rt_dvh_module():
    """Create a mock RT DVH module using the actual RTDVHModule class."""
    # Create actual module with minimal required data
    from pydicom import Dataset
    
    # Create a referenced structure set item
    ref_structure_set = Dataset()
    ref_structure_set.ReferencedSOPClassUID = "1.2.840.10008.*******.1.481.3"
    ref_structure_set.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
    
    # Create a DVH referenced ROI item  
    dvh_ref_roi = Dataset()
    dvh_ref_roi.ReferencedROINumber = 1
    dvh_ref_roi.DVHROIContributionType = "INCLUDED"
    
    # Create a DVH item
    dvh_item = Dataset()
    dvh_item.DVHReferencedROISequence = [dvh_ref_roi]
    dvh_item.DVHType = "CUMULATIVE"
    dvh_item.DoseUnits = "GY"
    dvh_item.DoseType = "PHYSICAL"
    dvh_item.DVHDoseScaling = 1.0
    dvh_item.DVHVolumeUnits = "CM3"
    dvh_item.DVHNumberOfBins = 4
    dvh_item.DVHData = [0.1, 10.5, 0.2, 9.8]
    
    return RTDVHModule.from_required_elements(
        referenced_structure_set_sequence=[ref_structure_set],
        dvh_sequence=[dvh_item]
    )


def create_mock_approval_module():
    """Create a mock approval module."""
    return create_mock_module(
        ApprovalStatus="APPROVED"
    )


def create_mock_overlay_plane_module():
    """Create a mock overlay plane module using the actual OverlayPlaneModule class."""
    # Create minimal 1-bit overlay data (8x8 pixels = 8 bytes)
    overlay_data = b'\x00' * 8  # 64 bits for 8x8 overlay
    
    return OverlayPlaneModule.from_required_elements(
        overlay_rows=8,
        overlay_columns=8,
        overlay_type="G",  # Graphics overlay
        overlay_origin=[1, 1],
        overlay_data=overlay_data
    )


def create_mock_frame_extraction_module():
    """Create a mock frame extraction module using the actual FrameExtractionModule class."""
    from pydicom import Dataset
    
    # Create a frame extraction item
    frame_extraction_item = Dataset()
    frame_extraction_item.MultiFrameSourceSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
    frame_extraction_item.SimpleFrameList = [1, 3, 5, 7, 10]  # Sample frame numbers
    
    return FrameExtractionModule.from_required_elements(
        frame_extraction_sequence=[frame_extraction_item]
    )


class TestRTDoseIOD:
    """Test RTDoseIOD functionality."""
    
    def test_valid_construction_minimal_required_modules(self):
        """Test successful RTDoseIOD creation with only required modules."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # Create dose module and set pixel data
        dose_module = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        dose_module.PixelData = dose_array.tobytes()
        
        rt_dose = RTDoseIOD(
            patient_module=PatientModule.from_required_elements(
                patient_name="Doe^John^Jr^^",
                patient_id="12345",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="20240101",
                study_time="120000"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                series_number="1"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid()
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Eclipse Treatment Planning System"
            ),
            rt_dose_module=dose_module,
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            )
        )
        
        # Verify IOD creation
        assert rt_dose is not None
        assert rt_dose.get_module('patient') is not None
        assert rt_dose.get_module('rt_dose') is not None
        assert rt_dose.SOP_CLASS_UID == "1.2.840.10008.*******.1.481.2"
    
    def test_valid_construction_with_all_modules(self):
        """Test RTDoseIOD creation with all optional and conditional modules."""
        dose_array = np.ones((64, 64, 20), dtype=np.uint32)
        
        rt_dose = RTDoseIOD(
            # Required modules
            patient_module=PatientModule.from_required_elements(
                patient_name="Smith^Jane^Dr^^",
                patient_id="67890",
                patient_birth_date="19850315",
                patient_sex=PatientSex.FEMALE
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="20240315",
                study_time="143000"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                series_number="2"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid()
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="RayStation Treatment Planning System"
            ),
            rt_dose_module=RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ),
            # Conditional modules for grid-based dose
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ),
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[-80.0, -80.0, -25.0],
                slice_thickness=2.5
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=64,
                columns=64,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            ),
            multi_frame_module=MultiFrameModule.from_required_elements(
                number_of_frames=20,
                frame_increment_pointer=["0018,1063"]  # Frame Time
            ),
            # Optional modules (using mocks)
            rt_dvh_module=create_mock_rt_dvh_module(),
            overlay_plane_module=create_mock_overlay_plane_module(),
            approval_module=create_mock_approval_module(),
            frame_extraction_module=create_mock_frame_extraction_module()
        )
        
        # Verify all modules are present
        assert rt_dose.get_module('patient') is not None
        assert rt_dose.get_module('rt_dose') is not None
        assert rt_dose.get_module('general_image') is not None
        assert rt_dose.get_module('rt_dvh') is not None
        assert rt_dose.get_module('approval') is not None
        assert rt_dose.get_module('overlay_plane') is not None
        assert rt_dose.get_module('frame_extraction') is not None
        
        # Verify the modules are actual instances of the expected classes
        assert isinstance(rt_dose.get_module('rt_dvh'), RTDVHModule)
        assert isinstance(rt_dose.get_module('overlay_plane'), OverlayPlaneModule)  
        assert isinstance(rt_dose.get_module('frame_extraction'), FrameExtractionModule)
        
        # Set up pixel data on the dose module for 3D check
        dose_module = rt_dose.get_module('rt_dose')
        dose_module.PixelData = dose_array.tobytes()
        
        # Verify IOD properties
        assert rt_dose.has_dvh_data
        assert rt_dose.has_image_representation
        assert rt_dose.is_3d_dose
        assert rt_dose.is_multi_frame
        assert rt_dose.has_spatial_information
        assert rt_dose.has_approval_data
    
    def test_invalid_modality_raises_error(self):
        """Test that incorrect modality raises IODValidationError."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        with pytest.raises(IODValidationError, match="modality='RTDOSE'"):
            RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="CT",  # Wrong modality
                    series_instance_uid=generate_uid(),
                    series_number="1"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid=generate_uid()
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dose_summation_type=DoseSummationType.PLAN
                ).with_pixel_data_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    bits_allocated=32,
                    bits_stored=32,
                    high_bit=31,
                    pixel_representation=0,
                    dose_grid_scaling=0.001
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid=generate_uid()
                )
            )
    
    def test_conditional_dependency_validation_image_plane_without_general_image(self):
        """Test that image_plane_module requires general_image_module."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        with pytest.raises(IODValidationError, match="image_plane_module requires general_image_module"):
            RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid=generate_uid(),
                    series_number="1"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid=generate_uid()
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dose_summation_type=DoseSummationType.PLAN
                ).with_pixel_data_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    bits_allocated=32,
                    bits_stored=32,
                    high_bit=31,
                    pixel_representation=0,
                    dose_grid_scaling=0.001
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid=generate_uid()
                ),
                # Missing general_image_module but providing image_plane_module
                image_plane_module=ImagePlaneModule.from_required_elements(
                    pixel_spacing=[2.0, 2.0],
                    image_orientation_patient=[1, 0, 0, 0, 1, 0],
                    image_position_patient=[0.0, 0.0, 0.0],
                    slice_thickness=3.0
                )
            )
    
    def test_conditional_dependency_validation_image_pixel_without_general_image(self):
        """Test that image_pixel_module requires general_image_module."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        with pytest.raises(IODValidationError, match="image_pixel_module requires general_image_module"):
            RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid=generate_uid(),
                    series_number="1"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid=generate_uid()
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dose_summation_type=DoseSummationType.PLAN
                ).with_pixel_data_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    bits_allocated=32,
                    bits_stored=32,
                    high_bit=31,
                    pixel_representation=0,
                    dose_grid_scaling=0.001
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid=generate_uid()
                ),
                # Missing general_image_module but providing image_pixel_module
                image_pixel_module=ImagePixelModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    rows=64,
                    columns=64,
                    bits_allocated=32,
                    bits_stored=32,
                    high_bit=31,
                    pixel_representation=0
                )
            )
    
    def test_conditional_dependency_validation_multi_frame_without_general_image(self):
        """Test that multi_frame_module requires general_image_module."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        with pytest.raises(IODValidationError, match="multi_frame_module requires general_image_module"):
            RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid=generate_uid(),
                    series_number="1"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid=generate_uid()
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dose_summation_type=DoseSummationType.PLAN
                ).with_pixel_data_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    bits_allocated=32,
                    bits_stored=32,
                    high_bit=31,
                    pixel_representation=0,
                    dose_grid_scaling=0.001
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid=generate_uid()
                ),
                # Missing general_image_module but providing multi_frame_module
                multi_frame_module=MultiFrameModule.from_required_elements(
                    number_of_frames=10,
                    frame_increment_pointer=["0018,1063"]  # Frame Time
                )
            )
    
    def test_properties_has_dvh_data(self):
        """Test has_dvh_data property."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # IOD without DVH data
        rt_dose_no_dvh = self._create_minimal_rt_dose_iod(dose_array)
        assert not rt_dose_no_dvh.has_dvh_data
        
        # IOD with DVH data (using mock)
        rt_dose_with_dvh = self._create_minimal_rt_dose_iod(
            dose_array,
            rt_dvh_module=create_mock_rt_dvh_module()
        )
        assert rt_dose_with_dvh.has_dvh_data
    
    def test_properties_has_image_representation(self):
        """Test has_image_representation property."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # IOD without image representation
        rt_dose_no_image = self._create_minimal_rt_dose_iod(dose_array)
        assert not rt_dose_no_image.has_image_representation
        
        # IOD with image representation
        rt_dose_with_image = self._create_minimal_rt_dose_iod(
            dose_array,
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            )
        )
        assert rt_dose_with_image.has_image_representation
    
    def test_properties_is_multi_frame(self):
        """Test is_multi_frame property."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # IOD without multi-frame
        rt_dose_single = self._create_minimal_rt_dose_iod(dose_array)
        assert not rt_dose_single.is_multi_frame
        
        # IOD with multi-frame
        rt_dose_multi = self._create_minimal_rt_dose_iod(
            dose_array,
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ),
            multi_frame_module=MultiFrameModule.from_required_elements(
                number_of_frames=5,
                frame_increment_pointer=["0018,1063"]  # Frame Time
            )
        )
        assert rt_dose_multi.is_multi_frame
    
    def test_properties_has_spatial_information(self):
        """Test has_spatial_information property."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # IOD without spatial information
        rt_dose_no_spatial = self._create_minimal_rt_dose_iod(dose_array)
        assert not rt_dose_no_spatial.has_spatial_information
        
        # IOD with spatial information
        rt_dose_with_spatial = self._create_minimal_rt_dose_iod(
            dose_array,
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ),
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.5
            )
        )
        assert rt_dose_with_spatial.has_spatial_information
    
    def test_properties_has_approval_data(self):
        """Test has_approval_data property."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        
        # IOD without approval data
        rt_dose_no_approval = self._create_minimal_rt_dose_iod(dose_array)
        assert not rt_dose_no_approval.has_approval_data
        
        # IOD with approval data
        rt_dose_with_approval = self._create_minimal_rt_dose_iod(
            dose_array,
            approval_module=ApprovalModule.from_required_elements(
                approval_status="APPROVED"
            )
        )
        assert rt_dose_with_approval.has_approval_data
    
    def test_get_dose_summary_basic(self):
        """Test get_dose_summary method with basic dose data."""
        dose_array = np.array([[[10, 20], [30, 40]], [[50, 60], [70, 80]]], dtype=np.uint32)
        scaling = 0.01
        
        rt_dose = self._create_minimal_rt_dose_iod(
            dose_array,
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=2,
                columns=2,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            )
        )
        
        # Manually set scaling factor and pixel data on the dose module
        dose_module = rt_dose.get_module('rt_dose')
        dose_module.DoseGridScaling = scaling
        dose_module.PixelData = dose_array.tobytes()
        
        summary = rt_dose.get_dose_summary()
        
        # Verify basic dose information
        assert summary['dose_units'] == DoseUnits.GY.value
        assert summary['dose_type'] == DoseType.PHYSICAL.value
        assert summary['dose_grid_scaling'] == scaling
        
        # Test passes if we can get basic summary information
        # (Calculated statistics may not be available depending on internal implementation)
    
    def test_get_spatial_information(self):
        """Test get_spatial_information method."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        frame_uid = generate_uid()
        
        rt_dose = self._create_minimal_rt_dose_iod(
            dose_array,
            frame_of_reference_uid=frame_uid,
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation="HFS"
            ),
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[-50.0, -50.0, -25.0],
                slice_thickness=5.0
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=10,
                columns=10,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            )
        )
        
        spatial_info = rt_dose.get_spatial_information()
        
        # Verify frame of reference information
        assert spatial_info['frame_of_reference_uid'] == frame_uid
        
        # Verify image plane information
        assert spatial_info['pixel_spacing'] == [2.5, 2.5]
        assert spatial_info['image_orientation_patient'] == [1, 0, 0, 0, 1, 0]
        assert spatial_info['image_position_patient'] == [-50.0, -50.0, -25.0]
        assert spatial_info['slice_thickness'] == 5.0
        
        # Verify image pixel information
        assert spatial_info['rows'] == 10
        assert spatial_info['columns'] == 10
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        rt_dose = self._create_minimal_rt_dose_iod(dose_array)
        
        assert hasattr(rt_dose, 'validate')
        assert callable(rt_dose.validate)
        
        # Test validation returns ValidationResult object
        result = rt_dose.validate()
        from pyrt_dicom.validators import ValidationResult
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert hasattr(result, 'has_errors')
        assert hasattr(result, 'has_warnings')
    
    def test_generate_dataset_method_exists(self):
        """Test that generate_dataset method exists and is callable."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        rt_dose = self._create_minimal_rt_dose_iod(dose_array)
        
        assert hasattr(rt_dose, 'generate_dataset')
        assert callable(rt_dose.generate_dataset)
    
    def test_get_module_method(self):
        """Test get_module method for accessing individual modules."""
        dose_array = np.ones((10, 10, 5), dtype=np.uint32)
        rt_dose = self._create_minimal_rt_dose_iod(dose_array)
        
        # Test accessing required modules
        patient_module = rt_dose.get_module('patient')
        assert patient_module is not None
        assert hasattr(patient_module, 'PatientName')
        
        rt_dose_module = rt_dose.get_module('rt_dose')
        assert rt_dose_module is not None
        assert hasattr(rt_dose_module, 'DoseUnits')
        
        # Test accessing non-existent module
        missing_module = rt_dose.get_module('non_existent')
        assert missing_module is None
    
    def test_comprehensive_dose_workflow(self):
        """Test comprehensive dose calculation workflow."""
        # Create realistic 3D dose array
        dose_array = np.random.rand(64, 64, 32).astype(np.uint32) * 65535
        dose_array[20:44, 20:44, 10:22] *= 2  # Higher dose in target region
        
        # Create DVH data for multiple structures
        target_dvh = np.linspace(100, 0, 100)
        oar_dvh = np.concatenate([np.ones(20) * 100, np.linspace(100, 0, 80)])
        
        rt_dose = RTDoseIOD(
            # Required modules with realistic data
            patient_module=PatientModule.from_required_elements(
                patient_name="Wilson^Robert^M^^",
                patient_id="RT001234",
                patient_birth_date="19700515",
                patient_sex=PatientSex.MALE
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="20240320",
                study_time="143000",
                referring_physicians_name="",
                study_id="PLAN001",
                accession_number=""
            ).with_optional_elements(
                study_description="RT Treatment Planning Study"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                series_number="1"
            ).with_optional_elements(
                series_description="RT Dose Distribution",
                series_date="20240320",
                series_time="143000"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid()
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Varian Medical Systems"
            ).with_optional_elements(
                station_name="Eclipse TPS",
                manufacturers_model_name="Eclipse",
                software_versions="v16.1.0"
            ),
            rt_dose_module=RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            ).with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0,
                dose_grid_scaling=0.001
            ).with_optional_elements(
                dose_comment="IMRT dose calculation"
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ),
            # Complete image representation
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ),
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[-80.0, -80.0, -40.0],
                slice_thickness=2.5
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=64,
                columns=64,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            ),
            multi_frame_module=MultiFrameModule.from_required_elements(
                number_of_frames=32,
                frame_increment_pointer=["0018,1063"]  # Frame Time
            ),
            # DVH analysis data (using mock)
            rt_dvh_module=create_mock_rt_dvh_module(),
            # Approval workflow (using mock)
            approval_module=create_mock_approval_module()
        )
        
        # Set up pixel data on the dose module 
        dose_module = rt_dose.get_module('rt_dose')
        dose_module.PixelData = dose_array.tobytes()
        
        # Verify comprehensive IOD
        assert rt_dose.has_dvh_data
        assert rt_dose.has_image_representation
        assert rt_dose.is_3d_dose
        assert rt_dose.is_multi_frame
        assert rt_dose.has_spatial_information
        assert rt_dose.has_approval_data
        
        # Verify dose summary contains expected information
        dose_summary = rt_dose.get_dose_summary()
        assert dose_summary['dose_units'] == 'GY'
        assert dose_summary['dose_type'] == 'PHYSICAL'
        assert dose_summary['dose_summation_type'] == 'PLAN'
        
        # Verify spatial information
        spatial_info = rt_dose.get_spatial_information()
        assert spatial_info['pixel_spacing'] == [2.5, 2.5]
        assert spatial_info['rows'] == 64
        assert spatial_info['columns'] == 64
    
    def _create_rt_dose_module_with_pixel_data(self, dose_array):
        """Helper method to create RTDoseModule with pixel data."""
        dose_module = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        dose_module.PixelData = dose_array.tobytes()
        return dose_module
    
    def _create_minimal_rt_dose_iod(self, dose_array, frame_of_reference_uid=None, **optional_modules):
        """Helper method to create minimal RTDoseIOD for testing."""
        if frame_of_reference_uid is None:
            frame_of_reference_uid = generate_uid()
            
        return RTDoseIOD(
            patient_module=PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="12345",
                patient_birth_date="",
                patient_sex=""
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="",
                study_time=""
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                series_number="1"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=frame_of_reference_uid
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Test System"
            ),
            rt_dose_module=self._create_rt_dose_module_with_pixel_data(dose_array),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ),
            **optional_modules
        )