"""
Test ApprovalModule (U - Optional) functionality.

ApprovalModule implements DICOM PS3.3 C.8.8.16 Approval Module.
Optional module for dose approval and authorization tracking.
"""

import pytest
from datetime import datetime
from pyrt_dicom.modules import ApprovalModule
from pyrt_dicom.enums import ApprovalStatus
from pyrt_dicom.validators import ValidationResult


class TestApprovalModule:
    """Test ApprovalModule (U - Optional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.APPROVED.value
    
    def test_approval_status_validation(self):
        """Test approval status enumeration validation."""
        for status in ApprovalStatus:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            assert approval.ApprovalStatus == status.value
    
    def test_with_review_information(self):
        """Test adding review information."""
        current_time = datetime.now()
        review_date = current_time.strftime("%Y%m%d")
        review_time = current_time.strftime("%H%M%S.%f")[:-3]
        
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date=review_date,
            review_time=review_time,
            reviewer_name="Smith^John^MD^^"
        )
        
        assert approval.ReviewDate == review_date
        assert approval.ReviewTime == review_time
        assert approval.ReviewerName == "Smith^John^MD^^"
    
    def test_review_info_validation(self):
        """Test validation of review information."""
        # Should not allow review info for UNAPPROVED status
        with pytest.raises(ValueError):
            ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.UNAPPROVED
            ).with_review_information(
                review_date="20230101",
                review_time="120000",
                reviewer_name="Smith^John"
            )
    
    def test_reviewer_name_formatting(self):
        """Test reviewer name DICOM Person Name formatting."""
        reviewer_names = [
            "Smith^John^MD^^",
            "Johnson^Mary^PhD^Dr.^",
            "Brown^Robert^^^III",
            "Wilson^Sarah^RO^^",
        ]
        
        for name in reviewer_names:
            approval = ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.APPROVED
            ).with_review_information(
                review_date="20230101",
                review_time="120000",
                reviewer_name=name
            )
            assert approval.ReviewerName == name
    
    def test_approval_workflow_statuses(self):
        """Test different approval workflow statuses and their properties."""
        workflow_scenarios = [
            (ApprovalStatus.APPROVED, "Final approval for treatment"),
            (ApprovalStatus.REJECTED, "Dose distribution needs revision"),
            (ApprovalStatus.UNAPPROVED, "Not yet submitted for approval")
        ]
        
        for status, comment in workflow_scenarios:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            
            assert approval.ApprovalStatus == status.value
            
            # Test property getters
            if status == ApprovalStatus.APPROVED:
                assert approval.is_approved
                assert not approval.is_rejected
                assert not approval.is_unapproved
                assert approval.requires_review_information
            elif status == ApprovalStatus.REJECTED:
                assert not approval.is_approved
                assert approval.is_rejected
                assert not approval.is_unapproved
                assert approval.requires_review_information
            else:  # UNAPPROVED
                assert not approval.is_approved
                assert not approval.is_rejected
                assert approval.is_unapproved
                assert not approval.requires_review_information
    
    def test_review_date_time_validation(self):
        """Test review date and time validation with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240315",
            review_time="143022.123456",
            reviewer_name="System^Test"
        )
        
        assert approval.ReviewDate == "20240315"
        assert approval.ReviewTime == "143022.123456"
        assert approval.ReviewerName == "System^Test"
        assert approval.has_review_information
        assert approval.is_configured
    
    def test_approval_authority_information(self):
        """Test approval authority and institutional information with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20230101",
            review_time="120000",
            reviewer_name="Wilson^James^MD^Dr.^"
        )
        
        assert approval.ReviewerName == "Wilson^James^MD^Dr.^"
        assert approval.has_review_information
        assert approval.requires_review_information
    
    def test_dose_specific_approval_comments(self):
        """Test basic approval functionality."""
        # This test verifies basic approval functionality
        # Note: The module doesn't store comments directly, but we can test the approval workflow
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        assert approval.is_configured
    
    def test_rejection_with_feedback(self):
        """Test rejection status with detailed feedback and property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.REJECTED
        ).with_review_information(
            review_date="20240320",
            review_time="091500",
            reviewer_name="Thompson^Lisa^MD^Dr.^"
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.REJECTED.value
        assert approval.ReviewerName == "Thompson^Lisa^MD^Dr.^"
        assert approval.is_rejected
        assert approval.requires_review_information
        assert approval.has_review_information
    
    def test_unapproved_workflow(self):
        """Test unapproved workflow status and properties."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
        assert not approval.requires_review_information
        assert not approval.has_review_information
        assert approval.is_configured
        assert approval.is_unapproved
        assert not approval.is_approved
        assert not approval.is_rejected
    
    def test_approval_sequence_tracking(self):
        """Test approval with review information and property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="100000",
            reviewer_name="Smith^John^MD^^"
        )
        
        assert approval.ReviewerName == "Smith^John^MD^^"
        assert approval.ReviewDate == "20240320"
        assert approval.ReviewTime == "100000"
        assert approval.has_review_information
        assert approval.is_configured
    
    def test_approval_digital_signature(self):
        """Test approval with digital signature information."""
        # Note: Digital signature handling would typically be separate from basic approval
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="120000",
            reviewer_name="System^Automated^Sig^^"
        )
        
        assert approval.ReviewerName == "System^Automated^Sig^^"
        assert approval.is_approved
        assert approval.requires_review_information
    
    def test_approval_version_tracking(self):
        """Test approval with version and modification tracking."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="143000.000",
            reviewer_name="System^Version^1.0"
        )
        
        assert approval.is_configured
        assert approval.has_review_information
        assert approval.ReviewerName == "System^Version^1.0"
    
    def test_qa_approval_workflow(self):
        """Test quality assurance approval workflow with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20230101",
            review_time="150000",
            reviewer_name="Davis^Michael^PhD^Prof.^"
        )
        
        assert approval.ReviewerName == "Davis^Michael^PhD^Prof.^"
        assert approval.is_approved
        assert approval.requires_review_information
        assert approval.has_review_information
    
    def test_unapproved_initial_state(self):
        """Test unapproved initial state for new dose calculations."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_approval_audit_trail(self):
        """Test approval audit trail elements."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            review_date="20240322",
            reviewer_name="Smith^John^MD^^"
        )
        
        assert approval.ReviewDate == "20240322"
        assert approval.ReviewerName == "Smith^John^MD^^"
    
    def test_optional_module_behavior(self):
        """Test that ApprovalModule behaves as optional enhancement."""
        # Approval module should not be required for basic dose functionality
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        # Module should function independently
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        assert hasattr(approval, 'validate')
        assert callable(approval.validate)
        
        # Test validation result structure
        validation_result = approval.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_approval_with_multiple_reviewers(self):
        """Test approval workflow with multiple reviewers."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="Wilson^James^MD^Dr.^",
        )
        
        assert approval.ReviewerName == "Wilson^James^MD^Dr.^"
    
    def test_treatment_machine_approval(self):
        """Test approval specific to treatment machine compatibility."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.APPROVED.value
        
