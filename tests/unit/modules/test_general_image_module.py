"""
Test GeneralImageModule (C - Conditional) functionality.

GeneralImageModule implements DICOM PS3.3 C.7.6.1 General Image Module.
Required when dose data is presented as image-based grid format.
"""

import pytest
from datetime import datetime
from pydicom.uid import generate_uid
from pyrt_dicom.modules import GeneralImageModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralImageModule:
    """Test GeneralImageModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""  # Can be empty for RT Dose
        )
        
        assert image.InstanceNumber == "1"
        assert hasattr(image, 'PatientOrientation')
    
    def test_instance_number_validation(self):
        """Test instance number validation."""
        # Valid integer strings only (per DICOM VR IS)
        valid_instance_numbers = ["1", "001", "123", "999"]
        
        for number in valid_instance_numbers:
            image = GeneralImageModule.from_required_elements(
                instance_number=number,
                patient_orientation=""
            )
            assert image.InstanceNumber == number
            
        # Setting an invalid instance number causes pydicom to raise a ValueError
        with pytest.raises(ValueError):
            image = GeneralImageModule.from_required_elements(
                instance_number="123a",  # Invalid IS value
                patient_orientation=""
            )
        
        # Warning: module validation will not catch the instance number error because
        # of the ValueError raised above
        result = image.validate()
        assert result.is_valid
        assert not result.has_errors
        
        # Basic creation should succeed with valid VR values
        image = GeneralImageModule.from_required_elements(
            instance_number="123",  # Valid IS value
            patient_orientation=""
        )
        result = image.validate()
        assert result.is_valid
        assert not result.has_errors
    
    def test_patient_orientation_values(self):
        """Test various patient orientation values."""
        # Common RT dose orientations
        orientations = [
            "",                    # Empty for dose grids
            "HFS",                 # Head First Supine
            "HFP",                 # Head First Prone
            "HFDR",                # Head First Decubitus Right
            "HFDL",                # Head First Decubitus Left
            "FFDR",                # Feet First Decubitus Right
            "FFDL",                # Feet First Decubitus Left
            "FFP",                 # Feet First Prone
            "FFS"                  # Feet First Supine
        ]
        
        for orientation in orientations:
            image = GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=orientation
            )
            assert image.PatientOrientation == orientation
    
    def test_with_optional_elements(self):
        """Test adding optional image elements."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation="HFS"
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"],
            image_comments="Test dose image",
            quality_control_image="NO",
            burned_in_annotation="NO"
        )
        
        assert hasattr(image, 'ImageType')
        assert image.ImageType == ["DERIVED", "SECONDARY", "DOSE"]
        assert hasattr(image, 'ImageComments')
        assert hasattr(image, 'QualityControlImage')
        assert hasattr(image, 'BurnedInAnnotation')
    
    def test_image_type_for_rt_dose(self):
        """Test image type values specific to RT dose."""
        rt_dose_image_types = [
            ["DERIVED", "SECONDARY", "DOSE"],
            ["DERIVED", "SECONDARY", "DOSE", "PLAN"],
            ["DERIVED", "SECONDARY", "DOSE", "BEAM"],
            ["DERIVED", "SECONDARY", "DOSE", "FRACTION"]
        ]
        
        for image_type in rt_dose_image_types:
            image = GeneralImageModule.from_required_elements(
                instance_number="1",
                patient_orientation=""
            ).with_optional_elements(
                image_type=image_type
            )
            assert image.ImageType == image_type
    
    def test_content_datetime_validation(self):
        """Test content date and time validation."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        ).with_temporal_elements(
            content_date="20240101",
            content_time="120000.123"
        )
        
        assert image.ContentDate == "20240101"
        assert image.ContentTime == "120000.123"
    
    def test_acquisition_elements(self):
        """Test that acquisition elements are not part of GeneralImageModule."""
        # Note: Acquisition elements are part of GeneralAcquisitionModule, not GeneralImageModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # These elements are not supported by GeneralImageModule
        assert not hasattr(image, 'AcquisitionNumber')
        assert not hasattr(image, 'AcquisitionDate')
        assert not hasattr(image, 'AcquisitionTime')
    
    def test_referenced_image_sequence(self):
        """Test that referenced image sequence is not part of GeneralImageModule."""
        # Note: Referenced Image Sequence is part of other modules (e.g., GeneralReferenceModule)
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # This element is not supported by GeneralImageModule
        assert not hasattr(image, 'ReferencedImageSequence')
    
    def test_derivation_description(self):
        """Test that derivation description is not part of GeneralImageModule."""
        # Note: Derivation Description is part of ImageDerivationModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # This element is not supported by GeneralImageModule
        assert not hasattr(image, 'DerivationDescription')
    
    def test_source_image_sequence(self):
        """Test that source image sequence is not part of GeneralImageModule."""
        # Note: Source Image Sequence is part of ImageDerivationModule  
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # This element is not supported by GeneralImageModule
        assert not hasattr(image, 'SourceImageSequence')
    
    def test_rt_dose_conditional_requirements(self):
        """Test conditional requirements for RT dose images."""
        # When presenting dose as image grid, GeneralImageModule is required
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""  # Often empty for dose grids
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"]
        )
        
        # Verify dose-specific requirements
        assert image.InstanceNumber == "1"
        assert "DOSE" in image.ImageType
    
    def test_image_orientation_patient(self):
        """Test that image orientation patient is not part of GeneralImageModule."""
        # Note: Image Orientation Patient is part of ImagePlaneModule
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # This element is not supported by GeneralImageModule
        assert not hasattr(image, 'ImageOrientationPatient')
    
    def test_image_position_patient(self):
        """Test that image position patient is not part of GeneralImageModule."""
        # Note: Image Position Patient is part of ImagePlaneModule  
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        # This element is not supported by GeneralImageModule
        assert not hasattr(image, 'ImagePositionPatient')
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        )
        
        assert hasattr(image, 'validate')
        assert callable(image.validate)
        
        # Test validation result structure
        validation_result = image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_conditional_usage_scenarios(self):
        """Test scenarios where GeneralImageModule is conditionally required."""
        # Scenario 1: Grid-based dose presentation requires GeneralImageModule
        grid_dose_image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""
        ).with_optional_elements(
            image_type=["DERIVED", "SECONDARY", "DOSE"]
        )
        
        # Scenario 2: DVH-only dose (no image grid) would not require this module
        # This would be tested in IOD-level tests
        
        assert grid_dose_image.ImageType == ["DERIVED", "SECONDARY", "DOSE"]
    
    def test_empty_patient_orientation_allowed(self):
        """Test that empty patient orientation is allowed for dose grids."""
        image = GeneralImageModule.from_required_elements(
            instance_number="1",
            patient_orientation=""  # Empty but not None (Type 2)
        )
        
        assert image.PatientOrientation == ""